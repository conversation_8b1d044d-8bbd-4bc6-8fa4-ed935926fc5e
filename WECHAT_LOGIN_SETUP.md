# 微信小程序登录配置指南

## 1. 前端配置（已完成）

### AppID 配置
- 文件：`project.config.json`
- 当前 AppID：`wxa1dd0760609970f8`

### 登录流程
- 调用 `Taro.login()` 获取 `code`
- 发送 `code` 到后端接口：`POST /community-x/auth/wechat/miniprogram`

## 2. 后端配置（需要配置）

### 环境变量配置
```bash
# 微信小程序配置
WECHAT_MINIPROGRAM_APP_ID=wxa1dd0760609970f8
WECHAT_MINIPROGRAM_APP_SECRET=您的AppSecret  # 从微信小程序后台获取
```

### 后端登录接口实现
```javascript
// POST /community-x/auth/wechat/miniprogram
app.post('/gustoenglish/wechat/register_openid', async (req, res) => {
  const { code } = req.body;
  
  // 1. 调用微信接口获取 openid 和 session_key
  const wechatResponse = await axios.get('https://api.weixin.qq.com/sns/jscode2session', {
    params: {
      appid: process.env.WECHAT_MINIPROGRAM_APP_ID,
      secret: process.env.WECHAT_MINIPROGRAM_APP_SECRET,
      js_code: code,
      grant_type: 'authorization_code'
    }
  });
  
  const { openid, session_key } = wechatResponse.data;
  
  // 2. 根据 openid 查找或创建用户
  let user = await User.findOne({ wechatOpenId: openid });
  if (!user) {
    user = await User.create({ wechatOpenId: openid });
  }
  
  // 3. 生成 JWT token
  const token = jwt.sign({ userId: user.id }, process.env.JWT_SECRET);
  
  // 4. 返回结果
  res.json({
    code: '0',
    data: { token },
    msg: 'success'
  });
});
```

## 3. 微信小程序后台配置

### 获取 AppSecret
1. 登录微信小程序后台：https://mp.weixin.qq.com
2. 开发 → 开发设置 → AppSecret
3. 点击"重置"获取新的 AppSecret（注意保密）

### 配置服务器域名
1. 开发 → 开发设置 → 服务器域名
2. 添加以下域名：
   - request 合法域名：
     - `https://api.wemore.com`
     - `https://apitest.wemore.com`
   - uploadFile 合法域名：
     - `https://api.wemore.com`
     - `https://apitest.wemore.com`

## 4. 调试步骤

### 检查前端
1. 确认 `Taro.login()` 能正常获取 `code`
2. 确认网络请求能正常发送到后端

### 检查后端
1. 确认接口 `/community-x/auth/wechat/miniprogram` 存在
2. 确认 AppSecret 配置正确
3. 确认能正常调用微信接口
4. 确认返回格式正确：
   ```json
   {
     "code": "0",
     "data": { "token": "your-jwt-token" },
     "msg": "success"
   }
   ```

### 检查微信后台
1. 确认服务器域名已配置
2. 确认 AppID 和 AppSecret 匹配

## 5. 常见错误

### 40013: invalid appid
- AppID 配置错误
- 检查前端 `project.config.json` 中的 appid

### 40125: invalid appsecret
- AppSecret 配置错误
- 重新获取 AppSecret 并配置到后端

### 网络请求失败
- 服务器域名未在小程序后台配置
- 检查域名配置是否正确

### 登录接口返回错误
- 后端接口逻辑有问题
- 检查后端日志和错误信息

## 6. 安全注意事项

1. **AppSecret 绝对不能放在前端代码中**
2. **AppSecret 应该配置在后端环境变量中**
3. **定期更换 AppSecret**
4. **使用 HTTPS 传输所有敏感数据**
