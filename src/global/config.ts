// 小程序配置信息
export const MINIPROGRAM_CONFIG = {
  // 小程序 AppID（前端可见）
  APP_ID: 'wxa1dd0760609970f8',
  
  // 后端 API 地址
  API_BASE_URL: {
    development: 'https://apitest.wemore.com',
    production: 'https://api.wemore.com'
  },
  
  // 登录相关配置
  LOGIN: {
    // 微信登录接口
    WECHAT_LOGIN_API: '/gustoenglish/wechat/register_openid',
    // 用户信息接口
    USER_INFO_API: '/community-x/user/info'
  }
}

// 注意：AppSecret 必须配置在后端服务器中，不能放在前端代码里！
// 后端需要的配置（仅供参考，不要放在前端）：
/*
WECHAT_MINIPROGRAM_CONFIG = {
  APP_ID: 'wxa1dd0760609970f8',
  APP_SECRET: '您的AppSecret', // 从微信小程序后台获取
  LOGIN_URL: 'https://api.weixin.qq.com/sns/jscode2session'
}
*/
