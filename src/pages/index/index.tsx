import { View, Text, Image } from '@tarojs/components'
import Taro, { useLoad } from '@tarojs/taro'
import { useEffect } from 'react'
import useUserStore from '../../store/user'
import Button from '../../components/Button'
import { PAGE_PATHS, EXAM_TYPES } from '../../global/consts'
import './index.scss'
import logoImg from '../../assets/images/logo.png'

export default function Index() {
  const { isLoggedIn } = useUserStore()

  useLoad(() => {
    console.log('Page loaded.')
  })

  useEffect(() => {
    console.log('Index页面 - isLoggedIn状态:', isLoggedIn)
    // 添加延迟检查登录状态，避免状态更新时机问题
    const checkLoginStatus = () => {
      if (!isLoggedIn) {
        console.log('未登录，跳转到登录页')
        Taro.redirectTo({
          url: PAGE_PATHS.LOGIN
        })
      }
    }

    // 延迟检查，给状态更新留出时间
    setTimeout(checkLoginStatus, 200)
  }, [isLoggedIn])

  const handleStartExam = (examType: string) => {
    if (!isLoggedIn) {
      Taro.navigateTo({
        url: PAGE_PATHS.LOGIN
      })
      return
    }

    Taro.navigateTo({
      url: `${PAGE_PATHS.QUIZ}?type=${examType}`
    })
  }

  const handleViewHistory = () => {
    Taro.navigateTo({
      url: PAGE_PATHS.MINE
    })
  }

  return (
    <View className="index">
      <View className="header">
        <Image
          className="logo"
          src={logoImg}
          mode="aspectFit"
        />
        <Text className="title">Gusto English</Text>
        <Text className="subtitle">高拓英语水平测试</Text>
      </View>

      <View className="exam-section">
        <Text className="section-title">选择测试类型</Text>

        <View className="exam-cards">
          <View className="exam-card">
            <View className="card-header">
              <Text className="card-title">月度评估测试</Text>
              <Text className="card-desc">全面评估您的英语水平</Text>
            </View>
            <View className="card-content">
              <Text className="card-info">• 包含听说读写四项技能</Text>
              <Text className="card-info">• 测试时间约30分钟</Text>
              <Text className="card-info">• 提供详细评估报告</Text>
            </View>
            <Button
              className="start-btn"
              onClick={() => handleStartExam(EXAM_TYPES.MONTH_ASSESSMENT)}
            >
              开始测试
            </Button>
          </View>

          <View className="exam-card">
            <View className="card-header">
              <Text className="card-title">模拟测试</Text>
              <Text className="card-desc">快速体验测试流程</Text>
            </View>
            <View className="card-content">
              <Text className="card-info">• 精选核心题型</Text>
              <Text className="card-info">• 测试时间约15分钟</Text>
              <Text className="card-info">• 快速了解测试形式</Text>
            </View>
            <Button
              className="start-btn"
              variant="outline"
              onClick={() => handleStartExam(EXAM_TYPES.DEV_MOCK)}
            >
              开始测试
            </Button>
          </View>
        </View>
      </View>

      <View className="quick-actions">
        <Button
          className="history-btn"
          variant="ghost"
          onClick={handleViewHistory}
        >
          查看历史成绩
        </Button>
      </View>
    </View>
  )
}
