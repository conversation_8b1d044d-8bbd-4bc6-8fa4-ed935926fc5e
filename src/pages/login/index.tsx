import { View, Text, Image, Input, Button as Ta<PERSON><PERSON>utton } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { useState } from 'react'
import useLogin from '../../hooks/useLogin'
import usePhoneLogin from '../../hooks/usePhoneLogin'
import Button from '../../components/Button'
import { PAGE_PATHS, EXAM_TYPES } from '../../global/consts'
import './index.scss'
import logoImg from '../../assets/images/logo.png'

export default function Login() {
  const { login, loading: oldLoginLoading } = useLogin()
  const {
    loading: phoneLoginLoading,
    phoneInfo,
    smsCodeSent,
    countdown,
    getWechatPhone,
    sendSmsCode,
    loginWithSmsCode,
    reset
  } = usePhoneLogin()

  const [agreementChecked, setAgreementChecked] = useState(false)
  const [loginStep, setLoginStep] = useState<'initial' | 'phone' | 'sms'>('initial')
  const [smsCode, setSmsCode] = useState('')

  const loading = oldLoginLoading || phoneLoginLoading

  // 处理微信获取手机号的回调
  const handleGetPhoneNumber = async (e: any) => {
    if (!agreementChecked) {
      Taro.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    console.log('微信获取手机号回调:', e.detail)

    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      const phone = await getWechatPhone(e.detail)

      if (phone) {
        setLoginStep('phone')
        console.log('获取到手机号:', phone)
      }
    } else {
      Taro.showToast({
        title: '获取手机号失败',
        icon: 'none'
      })
    }
  }

  // 发送验证码
  const handleSendSmsCode = async () => {
    const success = await sendSmsCode()
    if (success) {
      setLoginStep('sms')
    }
  }

  // 验证码登录
  const handleSmsLogin = async () => {
    if (!smsCode || smsCode.length !== 6) {
      Taro.showToast({
        title: '请输入6位验证码',
        icon: 'none'
      })
      return
    }

    console.log('开始验证码登录...')
    const success = await loginWithSmsCode(smsCode)
    console.log('登录结果:', success)

    if (success) {
      console.log('登录成功，准备跳转到测试页面')
      // 登录成功，稍作延迟后跳转到测试页面
      setTimeout(() => {
        console.log('执行跳转:', `${PAGE_PATHS.QUIZ}?type=${EXAM_TYPES.MONTH_ASSESSMENT}`)
        Taro.navigateTo({
          url: `${PAGE_PATHS.QUIZ}?type=${EXAM_TYPES.MONTH_ASSESSMENT}`
        }).then(() => {
          console.log('跳转成功')
        }).catch((error) => {
          console.error('跳转失败:', error)
          // 如果跳转失败，尝试重定向
          Taro.redirectTo({
            url: `${PAGE_PATHS.QUIZ}?type=${EXAM_TYPES.MONTH_ASSESSMENT}`
          })
        })
      }, 100)
    } else {
      console.log('登录失败，不执行跳转')
    }
  }

  // 重新开始登录流程
  const handleRestart = () => {
    reset()
    setLoginStep('initial')
    setSmsCode('')
  }

  // 旧的登录方法（保留用于测试）
  const handleLogin = async () => {
    if (!agreementChecked) {
      Taro.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    console.log('开始登录流程...')
    const success = await login()
    console.log('登录结果:', success)

    if (success) {
      console.log('登录成功，准备跳转到测试页面')
      // 登录成功，稍作延迟后跳转到测试页面，对应原来 web 端的 /quiz-new/Gusto_Public
      setTimeout(() => {
        console.log('执行跳转:', `${PAGE_PATHS.QUIZ}?type=${EXAM_TYPES.MONTH_ASSESSMENT}`)
        Taro.navigateTo({
          url: `${PAGE_PATHS.QUIZ}?type=${EXAM_TYPES.MONTH_ASSESSMENT}`
        }).then(() => {
          console.log('跳转成功')
        }).catch((error) => {
          console.error('跳转失败:', error)
          // 如果跳转失败，尝试重定向
          Taro.redirectTo({
            url: `${PAGE_PATHS.QUIZ}?type=${EXAM_TYPES.MONTH_ASSESSMENT}`
          })
        })
      }, 100)
    } else {
      console.log('登录失败，不执行跳转')
    }
  }

  const handleAgreementChange = () => {
    setAgreementChecked(!agreementChecked)
  }

  const handleViewAgreement = () => {
    // 查看用户协议
    Taro.showModal({
      title: '用户协议',
      content: '这里是用户协议内容...',
      showCancel: false
    })
  }

  const handleTestJump = () => {
    // 测试跳转功能
    console.log('测试跳转到测试页面')
    Taro.navigateTo({
      url: `${PAGE_PATHS.QUIZ}?type=${EXAM_TYPES.MONTH_ASSESSMENT}`
    }).then(() => {
      console.log('测试跳转成功')
    }).catch((error) => {
      console.error('测试跳转失败:', error)
    })
  }

  return (
    <View className="login-container">
      <View className="login-header">
        <Image
          className="logo"
          src={logoImg}
          mode="aspectFit"
          onError={(e) => {
            console.error('Image load error:', e)
          }}
          onLoad={() => {
            console.log('Image loaded successfully')
          }}
        />
        <Text className="title">Gusto English</Text>
        <Text className="subtitle">高拓英语水平测试</Text>
      </View>

      <View className="login-content">
        <View className="welcome-text">
          <Text className="welcome-title">欢迎使用</Text>
          <Text className="welcome-desc">
            专业的英语水平测试平台，为您提供准确的英语能力评估
          </Text>
        </View>

        <View className="login-form">
          {loginStep === 'initial' && (
            <>
              <TaroButton
                className="login-btn"
                openType="getPhoneNumber"
                onGetPhoneNumber={handleGetPhoneNumber}
                disabled={loading}
              >
                {loading ? '获取中...' : '获取手机号登录'}
              </TaroButton>

              <Button
                className="test-btn"
                onClick={handleLogin}
                disabled={loading}
              >
                {loading ? '登录中...' : '微信快速登录（测试）'}
              </Button>

              <Button
                className="test-btn"
                onClick={handleTestJump}
              >
                测试跳转
              </Button>
            </>
          )}

          {loginStep === 'phone' && phoneInfo && (
            <>
              <View className="phone-info">
                <Text className="phone-label">手机号：</Text>
                <Text className="phone-number">{phoneInfo.phoneNumber}</Text>
              </View>

              <Button
                className="login-btn"
                onClick={handleSendSmsCode}
                disabled={loading}
              >
                {loading ? '发送中...' : '发送验证码'}
              </Button>

              <Button
                className="back-btn"
                onClick={handleRestart}
              >
                重新选择
              </Button>
            </>
          )}

          {loginStep === 'sms' && (
            <>
              <View className="sms-input-section">
                <Text className="sms-label">请输入验证码</Text>
                <Input
                  className="sms-input"
                  type="number"
                  maxlength={6}
                  placeholder="请输入6位验证码"
                  value={smsCode}
                  onInput={(e) => setSmsCode(e.detail.value)}
                />

                <View className="sms-actions">
                  <Button
                    className="resend-btn"
                    onClick={handleSendSmsCode}
                    disabled={loading || countdown > 0}
                  >
                    {countdown > 0 ? `${countdown}s后重发` : '重新发送'}
                  </Button>
                </View>
              </View>

              <Button
                className="login-btn"
                onClick={handleSmsLogin}
                disabled={loading || !smsCode || smsCode.length !== 6}
              >
                {loading ? '登录中...' : '登录'}
              </Button>

              <Button
                className="back-btn"
                onClick={handleRestart}
              >
                重新开始
              </Button>
            </>
          )}

          <View className="agreement-section">
            <View
              className="checkbox-container"
              onClick={handleAgreementChange}
            >
              <View className={`checkbox ${agreementChecked ? 'checked' : ''}`}>
                {agreementChecked && <Text className="checkmark">✓</Text>}
              </View>
              <Text className="agreement-text">
                我已阅读并同意
                <Text
                  className="agreement-link"
                  onClick={handleViewAgreement}
                >
                  《用户协议》
                </Text>
              </Text>
            </View>
          </View>
        </View>
      </View>

      <View className="login-footer">
        <Text className="footer-text">
          登录即表示您同意我们的服务条款和隐私政策
        </Text>
      </View>
    </View>
  )
}
