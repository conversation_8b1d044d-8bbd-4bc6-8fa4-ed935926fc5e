import { View, Text, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { useState } from 'react'
import useLogin from '../../hooks/useLogin'
import Button from '../../components/Button'
import { PAGE_PATHS, EXAM_TYPES } from '../../global/consts'
import './index.scss'
import logoImg from '../../assets/images/logo.png'

export default function Login() {
  const { login, loading } = useLogin()
  const [agreementChecked, setAgreementChecked] = useState(false)

  const handleLogin = async () => {
    if (!agreementChecked) {
      Taro.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    console.log('开始登录流程...')
    const success = await login()
    console.log('登录结果:', success)

    if (success) {
      console.log('登录成功，准备跳转到测试页面')
      // 登录成功，稍作延迟后跳转到测试页面，对应原来 web 端的 /quiz-new/Gusto_Public
      setTimeout(() => {
        console.log('执行跳转:', `${PAGE_PATHS.QUIZ}?type=${EXAM_TYPES.MONTH_ASSESSMENT}`)
        Taro.navigateTo({
          url: `${PAGE_PATHS.QUIZ}?type=${EXAM_TYPES.MONTH_ASSESSMENT}`
        }).then(() => {
          console.log('跳转成功')
        }).catch((error) => {
          console.error('跳转失败:', error)
          // 如果跳转失败，尝试重定向
          Taro.redirectTo({
            url: `${PAGE_PATHS.QUIZ}?type=${EXAM_TYPES.MONTH_ASSESSMENT}`
          })
        })
      }, 100)
    } else {
      console.log('登录失败，不执行跳转')
    }
  }

  const handleAgreementChange = () => {
    setAgreementChecked(!agreementChecked)
  }

  const handleViewAgreement = () => {
    // 查看用户协议
    Taro.showModal({
      title: '用户协议',
      content: '这里是用户协议内容...',
      showCancel: false
    })
  }

  const handleTestJump = () => {
    // 测试跳转功能
    console.log('测试跳转到测试页面')
    Taro.navigateTo({
      url: `${PAGE_PATHS.QUIZ}?type=${EXAM_TYPES.MONTH_ASSESSMENT}`
    }).then(() => {
      console.log('测试跳转成功')
    }).catch((error) => {
      console.error('测试跳转失败:', error)
    })
  }

  return (
    <View className="login-container">
      <View className="login-header">
        <Image
          className="logo"
          src={logoImg}
          mode="aspectFit"
          onError={(e) => {
            console.error('Image load error:', e)
          }}
          onLoad={() => {
            console.log('Image loaded successfully')
          }}
        />
        <Text className="title">Gusto English</Text>
        <Text className="subtitle">高拓英语水平测试</Text>
      </View>

      <View className="login-content">
        <View className="welcome-text">
          <Text className="welcome-title">欢迎使用</Text>
          <Text className="welcome-desc">
            专业的英语水平测试平台，为您提供准确的英语能力评估
          </Text>
        </View>

        <View className="login-form">
          <Button
            className="login-btn"
            onClick={handleLogin}
            disabled={loading}
          >
            {loading ? '登录中...' : '微信快速登录'}
          </Button>

          <Button
            className="test-btn"
            onClick={handleTestJump}
          >
            测试跳转
          </Button>

          <View className="agreement-section">
            <View
              className="checkbox-container"
              onClick={handleAgreementChange}
            >
              <View className={`checkbox ${agreementChecked ? 'checked' : ''}`}>
                {agreementChecked && <Text className="checkmark">✓</Text>}
              </View>
              <Text className="agreement-text">
                我已阅读并同意
                <Text
                  className="agreement-link"
                  onClick={handleViewAgreement}
                >
                  《用户协议》
                </Text>
              </Text>
            </View>
          </View>
        </View>
      </View>

      <View className="login-footer">
        <Text className="footer-text">
          登录即表示您同意我们的服务条款和隐私政策
        </Text>
      </View>
    </View>
  )
}
