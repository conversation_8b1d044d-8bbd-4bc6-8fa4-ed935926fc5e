.login-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 40px 30px;
  background-color: #f8f9fa;
}

.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60px;

  .logo {
    width: 120px;
    height: 120px;
    margin-bottom: 20px;
  }

  .title {
    font-size: 28px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
  }

  .subtitle {
    font-size: 18px;
    color: #666;
  }
}

.login-content {
  flex: 1;
  display: flex;
  flex-direction: column;

  .welcome-text {
    margin-bottom: 40px;

    .welcome-title {
      font-size: 24px;
      font-weight: bold;
      color: #333;
      margin-bottom: 10px;
      display: block;
    }

    .welcome-desc {
      font-size: 16px;
      color: #666;
      line-height: 1.5;
    }
  }

  .login-form {
    .login-btn {
      width: 100%;
      height: 50px;
      background-color: #07c160;
      color: #fff;
      font-size: 18px;
      border-radius: 8px;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:disabled {
        opacity: 0.6;
      }
    }

    .test-btn {
      width: 100%;
      height: 45px;
      background: #f8f9fa;
      color: #666;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      font-size: 16px;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:active {
        background: #e9ecef;
      }
    }

    .back-btn {
      width: 100%;
      height: 45px;
      background: transparent;
      color: #999;
      border: 1px solid #ddd;
      border-radius: 8px;
      font-size: 16px;
      margin-top: 10px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:active {
        background: #f5f5f5;
      }
    }

    .phone-info {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 12px;
      margin-bottom: 30px;

      .phone-label {
        font-size: 16px;
        color: #666;
        margin-right: 10px;
      }

      .phone-number {
        font-size: 18px;
        font-weight: bold;
        color: #333;
      }
    }

    .sms-input-section {
      margin-bottom: 30px;

      .sms-label {
        display: block;
        font-size: 16px;
        color: #333;
        margin-bottom: 15px;
        text-align: center;
      }

      .sms-input {
        width: 100%;
        height: 50px;
        padding: 0 20px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 18px;
        text-align: center;
        letter-spacing: 8px;
        margin-bottom: 15px;
        box-sizing: border-box;

        &:focus {
          border-color: #07c160;
          outline: none;
        }
      }

      .sms-actions {
        display: flex;
        justify-content: center;

        .resend-btn {
          background: transparent;
          color: #07c160;
          border: none;
          font-size: 14px;
          padding: 5px 15px;

          &:disabled {
            color: #ccc;
          }

          &:active:not(:disabled) {
            color: #059652;
          }
        }
      }
    }

    .agreement-section {
      margin-top: 20px;

      .checkbox-container {
        display: flex;
        align-items: center;

        .checkbox {
          width: 20px;
          height: 20px;
          border: 1px solid #ddd;
          border-radius: 4px;
          margin-right: 10px;
          display: flex;
          align-items: center;
          justify-content: center;

          &.checked {
            background-color: #07c160;
            border-color: #07c160;
          }

          .checkmark {
            color: #fff;
            font-size: 14px;
          }
        }

        .agreement-text {
          font-size: 14px;
          color: #666;

          .agreement-link {
            color: #07c160;
          }
        }
      }
    }
  }
}

.login-footer {
  margin-top: 40px;
  text-align: center;

  .footer-text {
    font-size: 12px;
    color: #999;
  }
}
