import { View, Text, Image } from '@tarojs/components'
import Taro, { useLoad } from '@tarojs/taro'
import { useState, useEffect } from 'react'
import Button from '../../components/Button'
import useUserStore from '../../store/user'
import { getExamScoreApi } from '../../http/http'
import { PAGE_PATHS } from '../../global/consts'
import './index.scss'

interface ExamScore {
  id: string
  examType: string
  score: number
  maxScore: number
  completedAt: string
  level: string
}

export default function Mine() {
  const [scores, setScores] = useState<ExamScore[]>([])
  const [loading, setLoading] = useState(true)
  const { userInfo, isLoggedIn, logout } = useUserStore()

  useLoad(() => {
    console.log('Mine page loaded.')
    if (isLoggedIn) {
      loadScores()
    } else {
      setLoading(false)
    }
  })

  const loadScores = async () => {
    try {
      const res: any = await getExamScoreApi()

      if (res.code === '0' && res.data) {
        setScores(res.data)
      }
    } catch (error) {
      console.error('获取成绩失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleLogin = () => {
    Taro.navigateTo({
      url: PAGE_PATHS.LOGIN
    })
  }

  const handleLogout = () => {
    Taro.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          logout()
          setScores([])
        }
      }
    })
  }

  const handleViewCertificate = () => {
    Taro.navigateTo({
      url: PAGE_PATHS.CERTIFICATE
    })
  }

  const handleStartExam = () => {
    Taro.navigateTo({
      url: PAGE_PATHS.INDEX
    })
  }

  if (!isLoggedIn) {
    return (
      <View className="mine">
        <View className="login-prompt">
          <Image
            className="avatar-placeholder"
            src="/images/avatar-placeholder.png"
            mode="aspectFit"
          />
          <Text className="prompt-text">请先登录查看个人信息</Text>
          <Button
            className="login-btn"
            onClick={handleLogin}
          >
            立即登录
          </Button>
        </View>
      </View>
    )
  }

  return (
    <View className="mine">
      <View className="user-info">
        <Image
          className="avatar"
          src={userInfo && userInfo.avatar || '/images/avatar-placeholder.png'}
          mode="aspectFit"
        />
        <Text className="username">{userInfo && userInfo.name || '用户'}</Text>
        <Text className="user-id">ID: {userInfo && userInfo.id}</Text>
      </View>

      <View className="scores-section">
        <Text className="section-title">考试记录</Text>

        {loading ? (
          <View className="loading">
            <Text>加载中...</Text>
          </View>
        ) : scores.length > 0 ? (
          <View className="scores-list">
            {scores.map((score) => (
              <View key={score.id} className="score-item">
                <View className="score-header">
                  <Text className="exam-type">{score.examType}</Text>
                  <Text className="score-value">
                    {score.score}/{score.maxScore}
                  </Text>
                </View>
                <View className="score-details">
                  <Text className="level">等级: {score.level}</Text>
                  <Text className="date">{score.completedAt}</Text>
                </View>
              </View>
            ))}
          </View>
        ) : (
          <View className="empty-scores">
            <Text className="empty-text">暂无考试记录</Text>
            <Button
              className="start-exam-btn"
              onClick={handleStartExam}
            >
              开始测试
            </Button>
          </View>
        )}
      </View>

      <View className="actions-section">
        <Button
          className="action-btn"
          variant="outline"
          onClick={handleViewCertificate}
        >
          查看证书
        </Button>

        <Button
          className="action-btn logout-btn"
          variant="ghost"
          onClick={handleLogout}
        >
          退出登录
        </Button>
      </View>
    </View>
  )
}
