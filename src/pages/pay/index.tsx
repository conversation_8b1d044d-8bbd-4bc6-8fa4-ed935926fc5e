import { View, Text, Image } from '@tarojs/components'
import Taro, { useLoad } from '@tarojs/taro'
import { useState, useEffect } from 'react'
import Button from '../../components/Button'
import { getPaymentInfoApi } from '../../http/http'
import { createOrderAndPay } from '../../utils/payment'
import useUserStore from '../../store/user'
import { PAGE_PATHS } from '../../global/consts'
import './index.scss'

interface Product {
  id: string
  name: string
  description: string
  price: number
  originalPrice?: number
  features: string[]
}

export default function Pay() {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [payingProductId, setPayingProductId] = useState<string | null>(null)
  const { userInfo } = useUserStore()

  useLoad(() => {
    console.log('Pay page loaded.')
    loadProducts()
  })

  const loadProducts = async () => {
    try {
      const res: any = await getPaymentInfoApi()

      if (res.code === '0' && res.data) {
        setProducts(res.data)
      }
    } catch (error) {
      console.error('获取产品信息失败:', error)
      Taro.showToast({
        title: '获取产品信息失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }

  const handlePay = async (productId: string) => {
    if (!userInfo) {
      Taro.navigateTo({
        url: PAGE_PATHS.LOGIN
      })
      return
    }

    setPayingProductId(productId)

    try {
      const success = await createOrderAndPay(productId)

      if (success) {
        // 支付成功，返回上一页或跳转到考试页面
        setTimeout(() => {
          Taro.navigateBack()
        }, 1500)
      }
    } catch (error) {
      console.error('支付失败:', error)
    } finally {
      setPayingProductId(null)
    }
  }

  if (loading) {
    return (
      <View className="pay-loading">
        <Text>加载中...</Text>
      </View>
    )
  }

  return (
    <View className="pay">
      <View className="pay-header">
        <Text className="pay-title">选择套餐</Text>
        <Text className="pay-subtitle">解锁完整的英语水平测试功能</Text>
      </View>

      <View className="products-list">
        {products.map((product) => (
          <View key={product.id} className="product-card">
            <View className="product-header">
              <Text className="product-name">{product.name}</Text>
              <View className="product-price">
                <Text className="current-price">¥{product.price}</Text>
                {product.originalPrice && (
                  <Text className="original-price">¥{product.originalPrice}</Text>
                )}
              </View>
            </View>

            <Text className="product-description">{product.description}</Text>

            <View className="product-features">
              {product.features.map((feature, index) => (
                <View key={index} className="feature-item">
                  <Text className="feature-icon">✓</Text>
                  <Text className="feature-text">{feature}</Text>
                </View>
              ))}
            </View>

            <Button
              className="pay-btn"
              onClick={() => handlePay(product.id)}
              disabled={payingProductId === product.id}
            >
              {payingProductId === product.id ? '支付中...' : '立即购买'}
            </Button>
          </View>
        ))}
      </View>

      <View className="pay-footer">
        <Text className="footer-text">
          购买即表示您同意我们的服务条款和隐私政策
        </Text>
      </View>
    </View>
  )
}
