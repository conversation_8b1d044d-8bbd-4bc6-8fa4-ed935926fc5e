.pay {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 30px 20px;
  background-color: #f8f9fa;
}

.pay-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.pay-header {
  text-align: center;
  margin-bottom: 40px;

  .pay-title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
    display: block;
  }

  .pay-subtitle {
    font-size: 16px;
    color: #666;
    display: block;
  }
}

.products-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 40px;

  .product-card {
    background-color: #fff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    .product-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 15px;

      .product-name {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        flex: 1;
      }

      .product-price {
        display: flex;
        flex-direction: column;
        align-items: flex-end;

        .current-price {
          font-size: 20px;
          font-weight: bold;
          color: #07c160;
        }

        .original-price {
          font-size: 14px;
          color: #999;
          text-decoration: line-through;
        }
      }
    }

    .product-description {
      font-size: 14px;
      color: #666;
      margin-bottom: 20px;
      display: block;
    }

    .product-features {
      margin-bottom: 20px;

      .feature-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        .feature-icon {
          color: #07c160;
          font-weight: bold;
          margin-right: 8px;
        }

        .feature-text {
          font-size: 14px;
          color: #333;
        }
      }
    }

    .pay-btn {
      width: 100%;
      height: 45px;
      background-color: #07c160;
      color: #fff;
      border-radius: 8px;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.pay-footer {
  margin-top: auto;
  text-align: center;

  .footer-text {
    font-size: 12px;
    color: #999;
  }
}
