import { View, Text, Progress, Image, Textarea } from '@tarojs/components'
import Taro, { useRouter, useLoad } from '@tarojs/taro'
import { useState, useEffect } from 'react'
import useQuizStore from '../../store/quiz'
import useInitQuestion from '../../hooks/useInitQuestion'
import useRecorder from '../../hooks/useRecorder'
import Button from '../../components/Button'
import { publishAnswerApi } from '../../http/http'
import { setStorageJson, removeStorage } from '../../utils/storage'
import { STORAGE_KEYS, PAGE_PATHS } from '../../global/consts'
import './index.scss'

export default function Quiz() {
  const router = useRouter()
  // https://test.gustoenglish.com/quiz-new/Gusto_Public
  const examType = router.params.type || 'Gusto_Public'

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentAnswer, setCurrentAnswer] = useState('')

  const {
    examInfo,
    currentQuestionIndex,
    answers,
    isFinished,
    getCurrentQuestion,
    getTotalQuestions,
    getProgress,
    setCurrentQuestionIndex,
    addAnswer,
    updateAnswer,
    finishExam
  } = useQuizStore()

  const { loading, error } = useInitQuestion(examType)
  const { isRecording, recordingPath, startRecord, stopRecord, playRecord } = useRecorder()

  const currentQuestion = getCurrentQuestion()
  const totalQuestions = getTotalQuestions()
  const progress = getProgress()

  useLoad(() => {
    console.log('Quiz page loaded')
  })

  // 提交答案并进入下一题
  const handleNextQuestion = async () => {
    if (!currentQuestion) return

    // 保存当前答案
    const answer = {
      questionId: currentQuestion.id,
      answer: currentAnswer,
      audioUrl: recordingPath || undefined
    }

    const existingAnswerIndex = answers.findIndex(a => a.questionId === currentQuestion.id)
    if (existingAnswerIndex >= 0) {
      updateAnswer(currentQuestion.id, answer)
    } else {
      addAnswer(answer)
    }

    // 保存进度到本地存储
    const nextIndex = currentQuestionIndex + 1
    setStorageJson(STORAGE_KEYS.CURRENT_QUESTION, nextIndex)
    setStorageJson(STORAGE_KEYS.ANSWERS, [...answers, answer])

    if (nextIndex >= totalQuestions) {
      // 考试结束
      await handleSubmitExam([...answers, answer])
    } else {
      // 进入下一题
      setCurrentQuestionIndex(nextIndex)
      setCurrentAnswer('')
    }
  }

  // 提交整个考试
  const handleSubmitExam = async (finalAnswers = answers) => {
    if (!examInfo) return

    try {
      setIsSubmitting(true)

      await publishAnswerApi(examInfo.id, finalAnswers)

      // 清除本地缓存
      removeStorage(STORAGE_KEYS.EXAM_DATA)
      removeStorage(STORAGE_KEYS.CURRENT_QUESTION)
      removeStorage(STORAGE_KEYS.ANSWERS)

      finishExam()

      Taro.showToast({
        title: '提交成功',
        icon: 'success'
      })

      // 跳转到结果页面
      setTimeout(() => {
        Taro.navigateTo({
          url: `${PAGE_PATHS.ANSWER}?examId=${examInfo.id}`
        })
      }, 1500)

    } catch (error) {
      console.error('提交考试失败:', error)
      Taro.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // 录音控制
  const handleRecordToggle = async () => {
    if (isRecording) {
      stopRecord()
    } else {
      const success = await startRecord()
      if (!success) {
        Taro.showToast({
          title: '录音权限未开启',
          icon: 'none'
        })
      }
    }
  }

  if (loading) {
    return (
      <View className="quiz-loading">
        <Text>加载中...</Text>
      </View>
    )
  }

  if (error) {
    return (
      <View className="quiz-error">
        <Text>{error}</Text>
        <Button onClick={() => Taro.navigateBack()}>
          返回
        </Button>
      </View>
    )
  }

  if (isFinished) {
    return (
      <View className="quiz-finished">
        <Text className="finish-title">考试已完成</Text>
        <Text className="finish-desc">正在跳转到结果页面...</Text>
      </View>
    )
  }

  if (!currentQuestion) {
    return (
      <View className="quiz-error">
        <Text>没有找到题目</Text>
      </View>
    )
  }

  return (
    <View className="quiz-container">
      {/* 进度条 */}
      <View className="progress-section">
        <View className="progress-info">
          <Text className="progress-text">
            第 {currentQuestionIndex + 1} 题 / 共 {totalQuestions} 题
          </Text>
          <Text className="progress-percent">{Math.round(progress)}%</Text>
        </View>
        <Progress
          percent={progress}
          strokeWidth={4}
          activeColor="#07c160"
          backgroundColor="#e5e5e5"
        />
      </View>

      {/* 题目内容 */}
      <View className="question-section">
        <Text className="question-title">{currentQuestion.title}</Text>
        <Text className="question-content">{currentQuestion.content}</Text>

        {currentQuestion.imageUrl && (
          <Image
            className="question-image"
            src={currentQuestion.imageUrl}
            mode="aspectFit"
          />
        )}
      </View>

      {/* 答题区域 */}
      <View className="answer-section">
        {currentQuestion.type === 'text' && (
          <Textarea
            className="answer-input"
            placeholder="请输入您的答案"
            value={currentAnswer}
            onInput={(e) => setCurrentAnswer(e.detail.value)}
          />
        )}

        {(currentQuestion.type === 'audio' || currentQuestion.type === 'speaking') && (
          <View className="record-section">
            <Button
              className={`record-btn ${isRecording ? 'recording' : ''}`}
              onClick={handleRecordToggle}
            >
              {isRecording ? '停止录音' : '开始录音'}
            </Button>

            {recordingPath && (
              <Button
                className="play-btn"
                variant="outline"
                onClick={() => playRecord()}
              >
                播放录音
              </Button>
            )}
          </View>
        )}
      </View>

      {/* 操作按钮 */}
      <View className="action-section">
        <Button
          className="next-btn"
          onClick={handleNextQuestion}
          disabled={isSubmitting || (!currentAnswer && !recordingPath)}
        >
          {currentQuestionIndex === totalQuestions - 1
            ? (isSubmitting ? '提交中...' : '提交考试')
            : '下一题'
          }
        </Button>
      </View>
    </View>
  )
}
