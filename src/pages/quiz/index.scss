.quiz-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 30px 20px;
  background-color: #f8f9fa;
}

.progress-section {
  margin-bottom: 30px;
  
  .progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    
    .progress-text {
      font-size: 14px;
      color: #666;
    }
    
    .progress-percent {
      font-size: 14px;
      font-weight: bold;
      color: #333;
    }
  }
}

.question-section {
  margin-bottom: 30px;
  
  .question-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    display: block;
  }
  
  .question-content {
    font-size: 16px;
    color: #333;
    line-height: 1.6;
    margin-bottom: 20px;
    display: block;
  }
  
  .question-image {
    width: 100%;
    height: 200px;
    margin-bottom: 20px;
    border-radius: 8px;
  }
}

.answer-section {
  margin-bottom: 30px;
  
  .answer-input {
    width: 100%;
    height: 150px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    background-color: #fff;
  }
  
  .record-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
    
    .record-btn {
      width: 100%;
      height: 50px;
      border-radius: 8px;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &.recording {
        background-color: #ff4d4f;
        color: #fff;
      }
    }
    
    .play-btn {
      width: 100%;
      height: 50px;
      border-radius: 8px;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.action-section {
  margin-top: auto;
  padding-top: 20px;
  
  .next-btn {
    width: 100%;
    height: 50px;
    border-radius: 8px;
    font-size: 16px;
    background-color: #07c160;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.quiz-loading,
.quiz-error,
.quiz-finished {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 30px;
  
  .finish-title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
  }
  
  .finish-desc {
    font-size: 16px;
    color: #666;
    margin-bottom: 30px;
  }
}
