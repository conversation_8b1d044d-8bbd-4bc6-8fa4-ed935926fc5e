import Taro from '@tarojs/taro'
import { getStorage } from '../utils/storage'

// 创建请求配置
const request = {
  baseUrl: process.env.NODE_ENV === 'production'
    ? 'https://api.wemore.com'
    : 'https://apitest.wemore.com',

  get(url: string, data?: any) {
    return this.request(url, 'GET', data)
  },

  post(url: string, data?: any, headers?: Record<string, string>) {
    return this.request(url, 'POST', data, headers)
  },

  put(url: string, data?: any) {
    return this.request(url, 'PUT', data)
  },

  delete(url: string, data?: any) {
    return this.request(url, 'DELETE', data)
  },

  request(url: string, method: any, data?: any, customHeaders?: Record<string, string>) {
    const token = getStorage('token')

    console.log('发起请求:', {
      url: this.baseUrl + url,
      method,
      data,
      token: token ? '已设置' : '未设置'
    })

    const defaultHeaders = {
      'Content-Type': 'application/json',
      'X-5E-TOKEN': 'qJvKkSgEyrZLg3aoJn0DtRMwUt4Sbf3q'
    }

    const headers = customHeaders ? { ...defaultHeaders, ...customHeaders } : defaultHeaders

    return new Promise((resolve, reject) => {
      Taro.request({
        url: this.baseUrl + url,
        data,
        method,
        header: headers,
        success: (res) => {
          console.log('请求响应:', res)

          if (res.statusCode === 200) {
            resolve(res.data)
          } else {
            console.error('请求失败:', res)
            reject(new Error(`HTTP ${res.statusCode}: ${res.data && res.data.msg || '请求失败'}`))
          }
        },
        fail: (err) => {
          console.error('网络请求失败:', err)
          reject(err)
        }
      })
    })
  }
}

export { request }
