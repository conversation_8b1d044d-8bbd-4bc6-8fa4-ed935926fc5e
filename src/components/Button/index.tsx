import { View, Text } from '@tarojs/components'
import { ReactNode } from 'react'
import './index.scss'

export interface ButtonProps {
  children: ReactNode
  className?: string
  disabled?: boolean
  onClick?: () => void
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'default' | 'sm' | 'lg'
}

export function Button({
  children,
  className = '',
  variant = 'default',
  size = 'default',
  disabled = false,
  onClick,
  ...props
}: ButtonProps) {
  const baseClass = 'button'
  const variantClass = `button--${variant}`
  const sizeClass = `button--${size}`
  const disabledClass = disabled ? 'button--disabled' : ''
  
  const buttonClass = [baseClass, variantClass, sizeClass, disabledClass, className]
    .filter(Boolean)
    .join(' ')

  return (
    <View
      className={buttonClass}
      onClick={disabled ? undefined : onClick}
      {...props}
    >
      <Text>{children}</Text>
    </View>
  )
}

export default Button
