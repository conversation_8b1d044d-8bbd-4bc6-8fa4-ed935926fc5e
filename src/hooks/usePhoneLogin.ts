import { useState } from 'react'
import Taro from '@tarojs/taro'
import { getWechatPhoneApi, sendSmsCodeApi, phoneLoginApi, getUserInfoApi } from '../http/http'
import useUserStore from '../store/user'

export interface PhoneInfo {
  phoneNumber: string
  purePhoneNumber: string
  countryCode: string
}

export default function usePhoneLogin() {
  const [loading, setLoading] = useState(false)
  const [phoneInfo, setPhoneInfo] = useState<PhoneInfo | null>(null)
  const [smsCodeSent, setSmsCodeSent] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const { setToken, setUserInfo } = useUserStore()

  // 获取微信手机号 - 这个方法需要在button的getPhoneNumber事件中调用
  const getWechatPhone = async (phoneRes: any) => {
    try {
      setLoading(true)

      if (!phoneRes.code) {
        Taro.showToast({
          title: '获取手机号失败',
          icon: 'none'
        })
        return false
      }

      // 调用后端接口解密手机号
      const res: any = await getWechatPhoneApi(
        phoneRes.code,
        phoneRes.encryptedData,
        phoneRes.iv
      )

      if (res.code === 'success' && res.data) {
        const phone: PhoneInfo = {
          phoneNumber: res.data.phoneNumber,
          purePhoneNumber: res.data.purePhoneNumber,
          countryCode: res.data.countryCode
        }
        setPhoneInfo(phone)
        return phone
      } else {
        Taro.showToast({
          title: res.msg || '获取手机号失败',
          icon: 'none'
        })
        return false
      }
    } catch (error) {
      console.error('获取微信手机号失败:', error)
      Taro.showToast({
        title: '获取手机号失败',
        icon: 'none'
      })
      return false
    } finally {
      setLoading(false)
    }
  }

  // 发送验证码
  const sendSmsCode = async (phone?: string) => {
    try {
      setLoading(true)

      const targetPhone = phone || phoneInfo?.purePhoneNumber
      if (!targetPhone) {
        Taro.showToast({
          title: '请先获取手机号',
          icon: 'none'
        })
        return false
      }

      const res: any = await sendSmsCodeApi(targetPhone)

      if (res.code === 'success') {
        setSmsCodeSent(true)
        startCountdown()
        Taro.showToast({
          title: '验证码已发送',
          icon: 'success'
        })
        return true
      } else {
        Taro.showToast({
          title: res.msg || '发送验证码失败',
          icon: 'none'
        })
        return false
      }
    } catch (error) {
      console.error('发送验证码失败:', error)
      Taro.showToast({
        title: '发送验证码失败',
        icon: 'none'
      })
      return false
    } finally {
      setLoading(false)
    }
  }

  // 验证码登录
  const loginWithSmsCode = async (code: string, phone?: string) => {
    try {
      setLoading(true)

      const targetPhone = phone || phoneInfo?.purePhoneNumber
      if (!targetPhone) {
        Taro.showToast({
          title: '请先获取手机号',
          icon: 'none'
        })
        return false
      }

      if (!code || code.length !== 6) {
        Taro.showToast({
          title: '请输入6位验证码',
          icon: 'none'
        })
        return false
      }

      const res: any = await phoneLoginApi(targetPhone, code)

      if (res.code === 'success' && res.data && res.data.token) {
        // 保存token
        setToken(res.data.token)
        console.log('Token已保存:', res.data.token)

        // 获取用户信息
        const userInfoRes: any = await getUserInfoApi()
        console.log('用户信息响应:', userInfoRes)

        if (userInfoRes.code === '0' && userInfoRes.data) {
          setUserInfo(userInfoRes.data)
          console.log('用户信息已保存')
        }

        return true
      } else {
        Taro.showToast({
          title: res.msg || '验证码错误',
          icon: 'none'
        })
        return false
      }
    } catch (error) {
      console.error('验证码登录失败:', error)
      Taro.showToast({
        title: '登录失败',
        icon: 'none'
      })
      return false
    } finally {
      setLoading(false)
    }
  }

  // 开始倒计时
  const startCountdown = () => {
    setCountdown(60)
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer)
          return 0
        }
        return prev - 1
      })
    }, 1000)
  }

  // 重置状态
  const reset = () => {
    setPhoneInfo(null)
    setSmsCodeSent(false)
    setCountdown(0)
    setLoading(false)
  }

  return {
    loading,
    phoneInfo,
    smsCodeSent,
    countdown,
    getWechatPhone,
    sendSmsCode,
    loginWithSmsCode,
    reset
  }
}
