import { useState } from 'react'
import Taro from '@tarojs/taro'
import { wechatLoginApi, getUserInfoApi } from '../http/http'
import useUserStore from '../store/user'
import { PAGE_PATHS } from '../global/consts'

export default function useLogin() {
  const [loading, setLoading] = useState(false)
  const { setToken, setUserInfo } = useUserStore()

  // 微信登录
  const login = async () => {
    try {
      setLoading(true)

      const { code } = await Taro.login()
      const wx_app_id = "wx7310b7dfe575a933"
      console.log('微信登录 code:', code)
      console.log('即将请求登录接口:', '/gustoenglish/wechat/register_openid')

      const res: any = await wechatLoginApi(code, wx_app_id)
      console.log('登录接口响应:', res)

      if (res.code === 'success') {
        // console.log('登录成功，准备保存token:', res.data.token)
        // 保存token
        setToken("0a3dbj000xm7EU1xsY000M1bHP1dbj0l")
        console.log('Token已保存')

        // 获取用户信息
        const userInfoRes: any = await getUserInfoApi()
        console.log('用户信息响应:', userInfoRes)

        if (userInfoRes.code === '0' && userInfoRes.data) {
          setUserInfo(userInfoRes.data)
          console.log('用户信息已保存')
        }

        return true
      } else {
        console.error('登录失败响应:', res)
        Taro.showToast({
          title: res.msg || '登录失败',
          icon: 'none'
        })
        return false
      }
    } catch (error) {
      console.error('登录失败详细错误:', error)
      Taro.showToast({
        title: '登录失败',
        icon: 'none'
      })
      return false
    } finally {
      setLoading(false)
    }
  }

  // 检查登录状态
  const checkLogin = () => {
    const { isLoggedIn } = useUserStore.getState()

    if (!isLoggedIn) {
      Taro.navigateTo({
        url: PAGE_PATHS.LOGIN
      })
      return false
    }

    return true
  }

  return {
    login,
    checkLogin,
    loading
  }
}
