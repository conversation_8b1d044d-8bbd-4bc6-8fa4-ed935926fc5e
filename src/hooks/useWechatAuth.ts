import { useState } from 'react'
import Taro from '@tarojs/taro'
import useUserStore from '../store/user'
import { wechatLoginApi, getUserInfoApi } from '../http/http'

export default function useWechatAuth() {
  const [loading, setLoading] = useState(false)
  const { setToken, setUserInfo } = useUserStore()

  // 微信登录
  const login = async () => {
    try {
      setLoading(true)

      // 获取微信登录凭证
      const loginRes = await Taro.login()

      if (!loginRes.code) {
        Taro.showToast({
          title: '登录失败',
          icon: 'none'
        })
        return false
      }

      // 调用后端登录接口
      const res: any = await wechatLoginApi(loginRes.code)

      if (res.code === '0' && res.data && res.data.token) {
        // 保存token
        setToken(res.data.token)

        // 获取用户信息
        const userInfoRes: any = await getUserInfoApi()

        if (userInfoRes.code === '0' && userInfoRes.data) {
          setUserInfo(userInfoRes.data)
        }

        return true
      } else {
        Taro.showToast({
          title: '登录失败',
          icon: 'none'
        })
        return false
      }
    } catch (error) {
      console.error('登录失败:', error)
      Taro.showToast({
        title: '登录失败',
        icon: 'none'
      })
      return false
    } finally {
      setLoading(false)
    }
  }

  // 获取用户信息
  const getUserProfile = async () => {
    try {
      const res = await Taro.getUserProfile({
        desc: '用于完善会员资料'
      })

      return res.userInfo
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  }

  // 检查登录状态
  const checkSession = async () => {
    try {
      await Taro.checkSession()
      return true
    } catch (error) {
      console.error('登录状态已过期:', error)
      return false
    }
  }

  return {
    login,
    getUserProfile,
    checkSession,
    loading
  }
}
