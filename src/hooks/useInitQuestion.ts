import { useEffect, useState } from 'react'
import Taro from '@tarojs/taro'
import { getQuestionApi } from '../http/http'
import useQuizStore from '../store/quiz'
import { getStorageJson, setStorageJson } from '../utils/storage'
import { STORAGE_KEYS } from '../global/consts'

export default function useInitQuestion(examType: string) {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const { 
    setQuestions, 
    setExamInfo, 
    setCurrentQuestionIndex,
    reset
  } = useQuizStore()

  useEffect(() => {
    const initExam = async () => {
      try {
        setLoading(true)
        
        // 检查本地是否有缓存的考试数据
        const cachedData = getStorageJson(STORAGE_KEYS.EXAM_DATA)
        const cachedCurrentQuestion = getStorageJson(STORAGE_KEYS.CURRENT_QUESTION)
        const cachedAnswers = getStorageJson(STORAGE_KEYS.ANSWERS)
        
        if (cachedData && cachedData.type === examType) {
          // 使用缓存数据
          setExamInfo({
            id: cachedData.id,
            type: cachedData.type,
            title: cachedData.title,
            description: cachedData.description,
            totalQuestions: cachedData.questions.length,
            duration: cachedData.duration
          })
          
          setQuestions(cachedData.questions)
          
          if (cachedCurrentQuestion !== null) {
            setCurrentQuestionIndex(cachedCurrentQuestion)
          }
          
          // 恢复已保存的答案
          if (cachedAnswers && cachedAnswers.length > 0) {
            cachedAnswers.forEach(answer => {
              useQuizStore.getState().addAnswer(answer)
            })
          }
        } else {
          // 获取新的考试数据
          const res: any = await getQuestionApi({ exam_type: examType })
          
          if (res.code === '0' && res.data) {
            const examData = res.data
            
            setExamInfo({
              id: examData.id,
              type: examData.type,
              title: examData.title,
              description: examData.description,
              totalQuestions: examData.questions.length,
              duration: examData.duration
            })
            
            setQuestions(examData.questions)
            setCurrentQuestionIndex(0)
            
            // 缓存考试数据
            setStorageJson(STORAGE_KEYS.EXAM_DATA, examData)
            setStorageJson(STORAGE_KEYS.CURRENT_QUESTION, 0)
            setStorageJson(STORAGE_KEYS.ANSWERS, [])
          } else {
            throw new Error(res.msg || '获取考试数据失败')
          }
        }
      } catch (err) {
        console.error('初始化考试失败:', err)
        setError('获取考试数据失败，请重试')
        
        Taro.showToast({
          title: '获取考试数据失败',
          icon: 'none'
        })
      } finally {
        setLoading(false)
      }
    }

    // 重置考试状态
    reset()
    
    // 初始化考试
    initExam()
    
    // 组件卸载时清理
    return () => {
      // 可以在这里做一些清理工作
    }
  }, [examType])

  return {
    loading,
    error
  }
}
