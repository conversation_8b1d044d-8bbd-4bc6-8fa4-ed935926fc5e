import { create } from 'zustand'
import { getStorage, setStorage } from '../utils/storage'
import { STORAGE_KEYS } from '../global/consts'

export interface UserInfo {
  id: string
  name: string
  avatar: string
  phone: string
  email: string
  products: string[]
  [key: string]: any
}

interface UserState {
  token: string | null
  userInfo: UserInfo | null
  isLoggedIn: boolean

  // Actions
  setToken: (token: string) => void
  setUserInfo: (userInfo: UserInfo) => void
  logout: () => void

  // Getters
  hasProduct: (productId: string) => boolean
}

const useUserStore = create<UserState>((set, get) => ({
  token: getStorage(STORAGE_KEYS.TOKEN) || null,
  userInfo: getStorage(STORAGE_KEYS.USER_INFO) ? JSON.parse(getStorage(STORAGE_KEYS.USER_INFO)) : null,
  isLoggedIn: !!getStorage(STORAGE_KEYS.TOKEN),

  // Actions
  setToken: (token) => {
    setStorage(STORAGE_KEYS.TOKEN, token)
    set({ token, isLoggedIn: true })
  },

  setUserInfo: (userInfo) => {
    setStorage(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo))
    set({ userInfo })
  },

  logout: () => {
    setStorage(STORAGE_KEYS.TOKEN, '')
    setStorage(STORAGE_KEYS.USER_INFO, '')
    set({ token: null, userInfo: null, isLoggedIn: false })
  },

  // Getters
  hasProduct: (productId) => {
    const { userInfo } = get()
    return userInfo && userInfo.products && userInfo.products.includes(productId) || false
  }
}))



export default useUserStore
