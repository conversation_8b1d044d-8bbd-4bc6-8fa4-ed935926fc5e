import { create } from 'zustand'

export interface Answer {
  questionId: string
  answer: string
  audioUrl?: string
  duration?: number
}

export interface Question {
  id: string
  type: string
  title: string
  content: string
  options?: string[]
  audioUrl?: string
  imageUrl?: string
  readingTime?: number
  answerTime?: number
  tipInfo?: {
    content: string
  }
  introduction?: string
}

export interface ExamInfo {
  id: string
  type: string
  title: string
  description: string
  totalQuestions: number
  duration: number
}

interface QuizState {
  // 考试信息
  examInfo: ExamInfo | null
  questions: Question[]
  currentQuestionIndex: number
  answers: Answer[]
  
  // 考试状态
  isStarted: boolean
  isFinished: boolean
  isSubmitting: boolean
  
  // 计时器
  readingTimeLeft: number
  answerTimeLeft: number
  isReading: boolean
  
  // 录音状态
  isRecording: boolean
  recordingPath: string
  
  // Actions
  setExamInfo: (examInfo: ExamInfo) => void
  setQuestions: (questions: Question[]) => void
  setCurrentQuestionIndex: (index: number) => void
  addAnswer: (answer: Answer) => void
  updateAnswer: (questionId: string, answer: Partial<Answer>) => void
  
  startExam: () => void
  finishExam: () => void
  setSubmitting: (isSubmitting: boolean) => void
  
  setReadingTimeLeft: (time: number) => void
  setAnswerTimeLeft: (time: number) => void
  setIsReading: (isReading: boolean) => void
  
  setRecording: (isRecording: boolean) => void
  setRecordingPath: (path: string) => void
  
  reset: () => void
  
  // Getters
  getCurrentQuestion: () => Question | null
  getTotalQuestions: () => number
  getProgress: () => number
}

const useQuizStore = create<QuizState>((set, get) => ({
  // Initial state
  examInfo: null,
  questions: [],
  currentQuestionIndex: 0,
  answers: [],
  
  isStarted: false,
  isFinished: false,
  isSubmitting: false,
  
  readingTimeLeft: 0,
  answerTimeLeft: 0,
  isReading: false,
  
  isRecording: false,
  recordingPath: '',
  
  // Actions
  setExamInfo: (examInfo) => set({ examInfo }),
  setQuestions: (questions) => set({ questions }),
  setCurrentQuestionIndex: (index) => set({ currentQuestionIndex: index }),
  
  addAnswer: (answer) => set((state) => ({
    answers: [...state.answers, answer]
  })),
  
  updateAnswer: (questionId, answerUpdate) => set((state) => ({
    answers: state.answers.map(answer => 
      answer.questionId === questionId 
        ? { ...answer, ...answerUpdate }
        : answer
    )
  })),
  
  startExam: () => set({ isStarted: true }),
  finishExam: () => set({ isFinished: true }),
  setSubmitting: (isSubmitting) => set({ isSubmitting }),
  
  setReadingTimeLeft: (time) => set({ readingTimeLeft: time }),
  setAnswerTimeLeft: (time) => set({ answerTimeLeft: time }),
  setIsReading: (isReading) => set({ isReading }),
  
  setRecording: (isRecording) => set({ isRecording }),
  setRecordingPath: (path) => set({ recordingPath: path }),
  
  reset: () => set({
    examInfo: null,
    questions: [],
    currentQuestionIndex: 0,
    answers: [],
    isStarted: false,
    isFinished: false,
    isSubmitting: false,
    readingTimeLeft: 0,
    answerTimeLeft: 0,
    isReading: false,
    isRecording: false,
    recordingPath: ''
  }),
  
  // Getters
  getCurrentQuestion: () => {
    const state = get()
    return state.questions[state.currentQuestionIndex] || null
  },
  
  getTotalQuestions: () => get().questions.length,
  
  getProgress: () => {
    const state = get()
    return state.questions.length > 0 
      ? (state.currentQuestionIndex + 1) / state.questions.length * 100 
      : 0
  }
}))

export default useQuizStore
