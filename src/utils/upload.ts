import Taro from '@tarojs/taro'
import { getStorage } from './storage'
import { STORAGE_KEYS } from '../global/consts'

export const uploadFile = (filePath: string, fileName?: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    const token = getStorage(STORAGE_KEYS.TOKEN)

    Taro.uploadFile({
      url: process.env.NODE_ENV === 'production'
        ? 'https://api.wemore.com/upload'
        : 'https://apitest.wemore.com/upload',
      filePath,
      name: 'file',
      formData: {
        fileName: fileName || 'audio.mp3'
      },
      header: {
        'Authorization': token ? `Bearer ${token}` : ''
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data)
          if (data.code === '0') {
            resolve(data.data)
          } else {
            reject(new Error(data.msg || '上传失败'))
          }
        } catch (e) {
          reject(new Error('上传失败'))
        }
      },
      fail: (err) => {
        console.error('上传失败:', err)
        reject(err)
      }
    })
  })
}
