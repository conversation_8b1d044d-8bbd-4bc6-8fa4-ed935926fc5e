import Taro from '@tarojs/taro'

// 设置存储
export const setStorage = (key: string, value: any) => {
  try {
    Taro.setStorageSync(key, value)
  } catch (error) {
    console.error('设置存储失败:', error)
  }
}

// 获取存储
export const getStorage = (key: string) => {
  try {
    return Taro.getStorageSync(key)
  } catch (error) {
    console.error('获取存储失败:', error)
    return null
  }
}

// 获取JSON格式存储
export const getStorageJson = (key: string) => {
  try {
    const value = Taro.getStorageSync(key)
    return value ? JSON.parse(value) : null
  } catch (error) {
    console.error('获取JSON存储失败:', error)
    return null
  }
}

// 设置JSON格式存储
export const setStorageJson = (key: string, value: any) => {
  try {
    Taro.setStorageSync(key, JSON.stringify(value))
  } catch (error) {
    console.error('设置JSON存储失败:', error)
  }
}

// 移除存储
export const removeStorage = (key: string) => {
  try {
    Taro.removeStorageSync(key)
  } catch (error) {
    console.error('移除存储失败:', error)
  }
}

// 清空存储
export const clearStorage = () => {
  try {
    Taro.clearStorageSync()
  } catch (error) {
    console.error('清空存储失败:', error)
  }
}
