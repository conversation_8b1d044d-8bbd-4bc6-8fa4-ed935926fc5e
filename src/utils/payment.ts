import Taro from '@tarojs/taro'
import { createPaymentOrderApi, checkPaymentStatusApi } from '../http/http'

// 创建订单并发起支付
export const createOrderAndPay = async (productId: string): Promise<boolean> => {
  try {
    // 创建订单
    const orderRes: any = await createPaymentOrderApi(productId)

    if (orderRes.code !== '0' || !orderRes.data) {
      Taro.showToast({
        title: orderRes.msg || '创建订单失败',
        icon: 'none'
      })
      return false
    }

    const { orderId, payParams } = orderRes.data

    // 发起微信支付
    try {
      await Taro.requestPayment({
        timeStamp: payParams.timeStamp,
        nonceStr: payParams.nonceStr,
        package: payParams.package,
        signType: payParams.signType,
        paySign: payParams.paySign
      })

      // 支付成功，检查订单状态
      const checkRes: any = await checkPaymentStatusApi(orderId)

      if (checkRes.code === '0' && checkRes.data && checkRes.data.status === 'PAID') {
        Taro.showToast({
          title: '支付成功',
          icon: 'success'
        })
        return true
      } else {
        Taro.showToast({
          title: '支付状态异常，请联系客服',
          icon: 'none'
        })
        return false
      }
    } catch (payError: any) {
      if (payError.errMsg.indexOf('cancel') > -1) {
        Taro.showToast({
          title: '支付已取消',
          icon: 'none'
        })
      } else {
        Taro.showToast({
          title: '支付失败',
          icon: 'none'
        })
      }
      return false
    }
  } catch (error) {
    console.error('支付过程出错:', error)
    Taro.showToast({
      title: '支付失败',
      icon: 'none'
    })
    return false
  }
}

// 检查订单支付状态
export const checkOrderStatus = async (orderId: string): Promise<boolean> => {
  try {
    const res: any = await checkPaymentStatusApi(orderId)

    if (res.code === '0' && res.data && res.data.status === 'PAID') {
      return true
    } else {
      return false
    }
  } catch (error) {
    console.error('检查订单状态失败:', error)
    return false
  }
}
