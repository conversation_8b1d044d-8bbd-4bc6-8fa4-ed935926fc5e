/* 全局样式 - 移除 Tailwind 指令以兼容小程序 */
page {
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

/* 重置一些默认样式 */
view, text, image, button {
  box-sizing: border-box;
}

/* 按钮样式重置 */
button {
  border: none;
  outline: none;
  background: none;
  padding: 0;
  margin: 0;
}

button::after {
  border: none;
}

/* 基础工具类 - 替代 Tailwind */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.text-center {
  text-align: center;
}

.bg-primary {
  background-color: #07c160;
}

.text-white {
  color: #ffffff;
}

.text-gray-600 {
  color: #666666;
}

.text-gray-900 {
  color: #333333;
}

.rounded {
  border-radius: 4px;
}

.rounded-lg {
  border-radius: 8px;
}

.p-4 {
  padding: 16px;
}

.px-4 {
  padding-left: 16px;
  padding-right: 16px;
}

.py-2 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}
