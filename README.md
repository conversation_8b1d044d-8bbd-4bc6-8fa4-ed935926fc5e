# Gusto English 微信小程序

基于 Taro 框架开发的英语水平测试微信小程序，从 Next.js 项目迁移而来。

## 项目特性

- 🎯 **英语水平测试** - 支持多种题型的英语能力评估
- 🎤 **录音功能** - 支持口语测试和录音上传
- 💰 **支付功能** - 集成微信支付，支持产品购买
- 👤 **用户系统** - 微信授权登录，个人信息管理
- 📊 **成绩管理** - 考试记录查看和证书生成
- 📱 **小程序适配** - 完全适配微信小程序环境

## 技术栈

- **框架**: Taro 4.x + React 18
- **语言**: TypeScript
- **样式**: Tailwind CSS + Sass
- **状态管理**: Zustand
- **网络请求**: Taro.request (替代 axios)
- **构建工具**: Webpack 5

## 项目结构

```
src/
├── app.config.ts          # 小程序全局配置
├── app.scss              # 全局样式
├── app.ts                # 应用入口
├── components/           # 公共组件
│   └── Button/          # 按钮组件
├── global/              # 全局配置
│   └── consts.ts        # 常量定义
├── hooks/               # 自定义 Hooks
│   ├── useInitQuestion.ts
│   ├── useLogin.ts
│   ├── useRecorder.ts
│   └── useWechatAuth.ts
├── http/                # 网络请求
│   ├── api.ts           # 请求封装
│   └── http.ts          # 接口定义
├── pages/               # 页面
│   ├── index/           # 首页
│   ├── login/           # 登录页
│   ├── quiz/            # 考试页
│   ├── pay/             # 支付页
│   ├── mine/            # 个人中心
│   ├── certificate/     # 证书页
│   ├── answer/          # 答案详情
│   └── waiting/         # 等待页
├── store/               # 状态管理
│   ├── quiz.ts          # 考试状态
│   └── user.ts          # 用户状态
└── utils/               # 工具函数
    ├── storage.ts       # 存储工具
    ├── upload.ts        # 上传工具
    └── payment.ts       # 支付工具
```

## 开发指南

### 环境要求

- Node.js >= 16
- Yarn 或 npm
- 微信开发者工具

### 安装依赖

```bash
yarn install
```

### 开发命令

```bash
# 开发模式 - 微信小程序
yarn dev:weapp

# 构建 - 微信小程序
yarn build:weapp

# 其他平台
yarn dev:h5        # H5
yarn dev:alipay    # 支付宝小程序
yarn dev:tt        # 字节跳动小程序
```

### 配置说明

1. **小程序配置** (`src/app.config.ts`)
   - 页面路由配置
   - 权限配置（录音权限等）
   - 窗口样式配置

2. **环境配置**
   - 开发环境：`https://apitest.wemore.com`
   - 生产环境：`https://api.wemore.com`

3. **Tailwind CSS 配置** (`tailwind.config.js`)
   - 适配小程序环境
   - 自定义主题色彩
   - 禁用不兼容的功能

## 主要功能模块

### 1. 用户认证
- 微信授权登录
- 用户信息获取
- 登录状态管理

### 2. 考试系统
- 题目加载和展示
- 答题进度管理
- 录音功能集成
- 答案提交和保存

### 3. 支付系统
- 产品列表展示
- 微信支付集成
- 订单状态检查

### 4. 数据管理
- 本地存储管理
- 状态持久化
- 网络请求封装

## 部署说明

### 小程序发布

1. 使用微信开发者工具打开 `dist` 目录
2. 配置小程序 AppID
3. 上传代码并提交审核

### 环境变量

```bash
# 开发环境
NODE_ENV=development

# 生产环境
NODE_ENV=production
```

## 迁移说明

本项目从 Next.js 项目迁移而来，主要变更：

1. **框架迁移**: Next.js → Taro
2. **网络请求**: axios → Taro.request
3. **路由系统**: Next.js Router → Taro 页面配置
4. **样式系统**: 保留 Tailwind CSS，适配小程序
5. **状态管理**: 保留 Zustand
6. **小程序适配**: 添加录音、支付、授权等小程序特性

## 注意事项

1. **录音权限**: 需要在小程序后台配置录音权限
2. **支付功能**: 需要配置微信支付商户号
3. **域名配置**: 需要在小程序后台配置服务器域名
4. **版本兼容**: 确保基础库版本支持所使用的 API

## 开发建议

1. 使用微信开发者工具进行调试
2. 定期测试真机兼容性
3. 注意小程序包体积限制
4. 遵循微信小程序开发规范

## 许可证

MIT License
