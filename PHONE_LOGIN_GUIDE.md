# 手机号验证码登录流程说明

## 新的登录流程

原来的登录流程：点击"微信快速登录" → 直接获取token → 跳转到测试页面

新的登录流程：
1. 点击"获取手机号登录"按钮
2. 微信授权获取手机号
3. 显示手机号，点击"发送验证码"
4. 输入6位验证码
5. 验证成功后获取token
6. 跳转到测试页面

## 技术实现

### 1. 新增的API接口

在 `src/http/http.ts` 中添加了以下接口：

- `getWechatPhoneApi()` - 获取微信手机号（解密）
- `sendSmsCodeApi()` - 发送短信验证码
- `phoneLoginApi()` - 手机号验证码登录

### 2. 新增的Hook

创建了 `src/hooks/usePhoneLogin.ts`，包含：

- `getWechatPhone()` - 处理微信手机号获取
- `sendSmsCode()` - 发送验证码
- `loginWithSmsCode()` - 验证码登录
- 状态管理（loading、倒计时等）

### 3. 登录页面更新

`src/pages/login/index.tsx` 支持三个登录步骤：

- `initial` - 初始状态，显示"获取手机号登录"按钮
- `phone` - 显示获取到的手机号，可发送验证码
- `sms` - 输入验证码并登录

### 4. UI组件

- 使用 Taro 原生 Button 组件支持 `openType="getPhoneNumber"`
- 添加手机号显示、验证码输入框
- 倒计时功能防止频繁发送验证码

## 后端接口要求

需要后端实现以下接口：

### 1. 获取微信手机号接口
```
POST /gustoenglish/wechat/phone
{
  "code": "微信返回的code",
  "encryptedData": "加密数据",
  "iv": "初始向量"
}

响应：
{
  "code": "success",
  "data": {
    "phoneNumber": "+86 138****1234",
    "purePhoneNumber": "13812341234",
    "countryCode": "86"
  }
}
```

### 2. 发送验证码接口
```
POST /gustoenglish/sms/send
{
  "phone": "13812341234"
}

响应：
{
  "code": "success",
  "msg": "验证码已发送"
}
```

### 3. 验证码登录接口
```
POST /gustoenglish/auth/phone-login
{
  "phone": "13812341234",
  "code": "123456"
}

响应：
{
  "code": "success",
  "data": {
    "token": "用户token"
  }
}
```

## 测试步骤

1. 启动开发服务器：`yarn run dev:weapp`
2. 在微信开发者工具中打开项目
3. 进入登录页面
4. 点击"获取手机号登录"按钮
5. 授权获取手机号
6. 点击"发送验证码"
7. 输入验证码
8. 验证登录成功

## 注意事项

1. **微信小程序配置**：需要在微信小程序后台配置获取手机号权限
2. **后端接口**：需要后端实现对应的API接口
3. **测试环境**：在开发环境中可能需要使用测试手机号
4. **错误处理**：已添加完整的错误提示和状态管理
5. **兼容性**：保留了原来的"微信快速登录（测试）"按钮用于测试

## 文件变更列表

- ✅ `src/http/http.ts` - 添加新的API接口
- ✅ `src/http/api.ts` - 修复请求方法支持自定义headers
- ✅ `src/hooks/usePhoneLogin.ts` - 新增手机号登录Hook
- ✅ `src/pages/login/index.tsx` - 更新登录页面逻辑和UI
- ✅ `src/pages/login/index.scss` - 添加新的样式
- ✅ `PHONE_LOGIN_GUIDE.md` - 本说明文档
