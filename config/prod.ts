import type { UserConfigExport } from "@tarojs/cli"

export default {
  mini: {
    // 通过 webpack 配置启用 JS 压缩
    webpackChain(chain) {
      // 启用生产模式优化
      chain.mode('production')

      // 启用代码压缩
      chain.optimization.minimize(true)

      // 配置压缩选项
      if (process.env.NODE_ENV === 'production') {
        // 先定义 terser 插件，然后配置
        const TerserPlugin = require('terser-webpack-plugin')

        chain.optimization.minimizer('terser').use(TerserPlugin, [{
          terserOptions: {
            compress: {
              drop_console: true,
              drop_debugger: true,
              pure_funcs: ['console.log', 'console.info']
            },
            mangle: {
              toplevel: false
            },
            format: {
              comments: false
            }
          },
          extractComments: false,
          parallel: true
        }])
      }
    }
  },
  h5: {
    /**
     * WebpackChain 插件配置
     * @docs https://github.com/neutrinojs/webpack-chain
     */
    // webpackChain (chain) {
    //   /**
    //    * 如果 h5 端编译后体积过大，可以使用 webpack-bundle-analyzer 插件对打包体积进行分析。
    //    * @docs https://github.com/webpack-contrib/webpack-bundle-analyzer
    //    */
    //   chain.plugin('analyzer')
    //     .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin, [])
    //   /**
    //    * 如果 h5 端首屏加载时间过长，可以使用 prerender-spa-plugin 插件预加载首页。
    //    * @docs https://github.com/chrisvfritz/prerender-spa-plugin
    //    */
    //   const path = require('path')
    //   const Prerender = require('prerender-spa-plugin')
    //   const staticDir = path.join(__dirname, '..', 'dist')
    //   chain
    //     .plugin('prerender')
    //     .use(new Prerender({
    //       staticDir,
    //       routes: [ '/pages/index/index' ],
    //       postProcess: (context) => ({ ...context, outputPath: path.join(staticDir, 'index.html') })
    //     }))
    // }
  }
} satisfies UserConfigExport<'webpack5'>
