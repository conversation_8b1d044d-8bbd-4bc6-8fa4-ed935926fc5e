/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#07c160',
          foreground: '#ffffff',
        },
        secondary: {
          DEFAULT: '#f8f9fa',
          foreground: '#333333',
        },
        destructive: {
          DEFAULT: '#ff4d4f',
          foreground: '#ffffff',
        },
        accent: {
          DEFAULT: '#f0f0f0',
          foreground: '#333333',
        },
        border: '#e5e5e5',
        input: '#e5e5e5',
        ring: '#07c160',
        background: '#ffffff',
        foreground: '#333333',
      },
      borderRadius: {
        lg: '8px',
        md: '6px',
        sm: '4px',
      },
    },
  },
  plugins: [],
  corePlugins: {
    // 禁用一些在小程序中不支持的功能
    preflight: false,
  }
}
